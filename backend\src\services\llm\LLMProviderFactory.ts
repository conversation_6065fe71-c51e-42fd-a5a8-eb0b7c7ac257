import { LL<PERSON>rovider } from './interfaces';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { AzureOpenAIProvider } from './providers/AzureOpenAIProvider';

export type ProviderType = 'openai' | 'azure';

export class LLMProviderFactory {
  private static instance: LLMProvider | null = null;
  private static currentProviderType: ProviderType | null = null;

  static createProvider(type: ProviderType): LLMProvider {
    switch (type) {
      case 'openai':
        return new OpenAIProvider();
      case 'azure':
        return new AzureOpenAIProvider();
      default:
        throw new Error(`Unsupported LLM provider: ${type}`);
    }
  }

  static getInstance(): LLMProvider {
    const providerType = (process.env.LLM_PROVIDER as ProviderType) || 'openai';

    // Återanvänd instans om samma provider-typ
    if (this.instance && this.currentProviderType === providerType) {
      return this.instance;
    }

    // Skapa ny instans
    this.instance = this.createProvider(providerType);
    this.currentProviderType = providerType;

    return this.instance;
  }

  static getDefaultModel(): string {
    return process.env.LLM_DEFAULT_MODEL || 'gpt-4o-mini';
  }

  static getFallbackModel(): string {
    return process.env.LLM_FALLBACK_MODEL || 'gpt-4o-mini';
  }

  static reset(): void {
    this.instance = null;
    this.currentProviderType = null;
  }
}
