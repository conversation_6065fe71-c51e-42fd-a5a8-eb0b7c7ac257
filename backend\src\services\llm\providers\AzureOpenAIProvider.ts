import { OpenAI } from 'openai';
import { Base<PERSON><PERSON>rovider } from '../BaseLLMProvider';
import { ChatCompletionRequest, ChatCompletionResponse } from '../interfaces';
import { ModelRegistry } from '../ModelRegistry';

export class AzureOpenAIProvider extends BaseLL<PERSON>rovider {
  name = 'azure';
  private client: OpenAI;

  constructor() {
    super();

    const requiredEnvVars = [
      'AZURE_OPENAI_API_KEY',
      'AZURE_OPENAI_ENDPOINT',
      'AZURE_OPENAI_API_VERSION'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`${envVar} environment variable is required for Azure OpenAI`);
      }
    }

    this.client = new OpenAI({
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      baseURL: `${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments`,
      defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION },
      defaultHeaders: {
        'api-key': process.env.AZURE_OPENAI_API_KEY,
      }
    });
  }

  isConfigured(): boolean {
    return !!(
      process.env.AZURE_OPENAI_API_KEY &&
      process.env.AZURE_OPENAI_ENDPOINT &&
      process.env.AZURE_OPENAI_API_VERSION
    );
  }

  getSupportedModels(): string[] {
    return ModelRegistry.getModelsForProvider('azure').map(m => m.id);
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const modelConfig = ModelRegistry.getModel(request.model, 'azure');
    if (!modelConfig) {
      throw new Error(`Unsupported model: ${request.model} for Azure provider`);
    }

    const completion = await this.client.chat.completions.create({
      model: modelConfig.providerModel, // Azure deployment name
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from Azure OpenAI');
    }

    return {
      content,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }
}
