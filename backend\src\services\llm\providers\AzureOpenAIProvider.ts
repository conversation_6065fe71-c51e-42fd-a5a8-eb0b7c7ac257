import { OpenAI } from 'openai';
import { BaseLLMProvider } from '../BaseLLMProvider';
import { ChatCompletionRequest, ChatCompletionResponse } from '../interfaces';
import { ModelRegistry } from '../ModelRegistry';

export class AzureOpenAIProvider extends BaseLLMProvider {
  name = 'azure';
  private client: OpenAI | null = null;

  constructor() {
    super();

    // Only initialize client if all required env vars are present
    if (this.isConfigured()) {
      this.client = new OpenAI({
        apiKey: process.env.AZURE_OPENAI_API_KEY,
        baseURL: `${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments`,
        defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION },
        defaultHeaders: {
          'api-key': process.env.AZURE_OPENAI_API_KEY,
        }
      });
    }
  }

  isConfigured(): boolean {
    const hasKey = !!process.env.AZURE_OPENAI_API_KEY;
    const hasEndpoint = !!process.env.AZURE_OPENAI_ENDPOINT;
    const hasVersion = !!process.env.AZURE_OPENAI_API_VERSION;

    console.log('🔍 Azure OpenAI Configuration Check:');
    console.log('- API Key:', hasKey ? 'Present' : 'Missing');
    console.log('- Endpoint:', hasEndpoint ? 'Present' : 'Missing');
    console.log('- Version:', hasVersion ? 'Present' : 'Missing');
    console.log('- All configured:', hasKey && hasEndpoint && hasVersion);

    return hasKey && hasEndpoint && hasVersion;
  }

  getSupportedModels(): string[] {
    return ModelRegistry.getModelsForProvider('azure').map(m => m.id);
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    if (!this.client) {
      throw new Error('Azure OpenAI client not initialized. Check your environment variables.');
    }

    const modelConfig = ModelRegistry.getModel(request.model, 'azure');
    if (!modelConfig) {
      throw new Error(`Unsupported model: ${request.model} for Azure provider`);
    }

    const completion = await this.client.chat.completions.create({
      model: modelConfig.providerModel, // Azure deployment name
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from Azure OpenAI');
    }

    return {
      content,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }
}
