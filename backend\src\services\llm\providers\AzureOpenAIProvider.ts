import { OpenA<PERSON> } from 'openai';
import { Base<PERSON><PERSON>rovider } from '../BaseLLMProvider';
import { ChatCompletionRequest, ChatCompletionResponse } from '../interfaces';
import { ModelRegistry } from '../ModelRegistry';

export class AzureOpenAIProvider extends Base<PERSON><PERSON>rovider {
  name = 'azure';
  private client: OpenAI | null = null;

  constructor() {
    super();

    // Only initialize client if all required env vars are present
    if (this.isConfigured()) {
      this.client = new OpenAI({
        apiKey: process.env.AZURE_OPENAI_API_KEY,
        baseURL: `${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments`,
        defaultQuery: { 'api-version': process.env.AZURE_OPENAI_API_VERSION },
        defaultHeaders: {
          'api-key': process.env.AZURE_OPENAI_API_KEY,
        }
      });
    }
  }

  isConfigured(): boolean {
    return !!(
      process.env.AZURE_OPENAI_API_KEY &&
      process.env.AZURE_OPENAI_ENDPOINT &&
      process.env.AZURE_OPENAI_API_VERSION
    );
  }

  getSupportedModels(): string[] {
    return ModelRegistry.getModelsForProvider('azure').map(m => m.id);
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    if (!this.client) {
      throw new Error('Azure OpenAI client not initialized. Check your environment variables.');
    }

    const modelConfig = ModelRegistry.getModel(request.model, 'azure');
    if (!modelConfig) {
      throw new Error(`Unsupported model: ${request.model} for Azure provider`);
    }

    const completion = await this.client.chat.completions.create({
      model: modelConfig.providerModel, // Azure deployment name
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from Azure OpenAI');
    }

    return {
      content,
      usage: completion.usage ? {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      } : undefined
    };
  }
}
